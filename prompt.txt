**Role**
you are an expert Vue.JS / Electron engineer, 

**Objective**
write the full project and source code of a Vue.Js based electron production ready application. The application consiste in the generation of well crafted prompt using 

**Features** 
-A system menu with File (submenu: Select datafolder,separator, exit) , Edit (submenu: Copy, Cut, Paste), About.
-A left panel composed of :
	- 5 editable dropdown lists named Role, Objecttive, Feature, Constraint, Approach populated  by matching json file stored in a dedicated data folder (file the json file with sample data). When edited the content of the edited dropdown is added to the matching Json file if not already existed . On the right side  of the dropdown a button "Add" copy the content of the selected dropdown item in the main window and change the color of the dropdown and the associated button (to signal already used)
	- 2 checkbox with the labels "Reflect on a step by step approach" and "Slice you response for not exceding the maximum output tockens number and ask the user to type * to continue".
-A main right panel where the prompt is displayed (the raw text), the prompt can also be edited from this window directly. The main windows include also 3 buttons:
	- A button "Save" allows to save the created prompt in a json file in the data folder (the crafted prompt).
	- A button "Copy" allows to copy the content of the main windows text (the crafted prompt)
	- A button "Clear" allows to empty the main windows text (the crafted prompt).

**Constraints**
The project use shadcn/UI for styling the Vue.JS 3 project and electron for producing a cross platform application. Provide the possibility to extend easily the application. and make the source code readable.
Define a very well designed UI for user friendlyness and clarity

**Approach**
Reflect on the architecture, the file structure and the full source code by using a step by step approach for decomposing complex problem in smaller one and keep coherence straight. use Vue.JS , shadcn and electron.

Do not use Canvas

