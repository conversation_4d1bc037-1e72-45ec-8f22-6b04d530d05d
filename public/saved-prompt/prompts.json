[{"id": "prompt1", "content": "Design a low-power IoT sensor with WiFi, Bluetooth, and OTA update support."}, {"id": "prompt2", "content": "Create a waterproof wearable with a touchscreen and long battery life."}, {"id": "prompt3", "content": "Develop a device that optimizes cost without compromising reliability or user experience."}, {"id": "prompt4", "content": "Propose a rapid prototyping approach for a smart home controller supporting multiple wireless protocols."}]