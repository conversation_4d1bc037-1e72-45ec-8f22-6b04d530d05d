import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import electron from 'vite-plugin-electron/simple';

export default defineConfig({
  define: {
    __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: JSON.stringify(false),
  },
  plugins: [
    vue(),
    electron({
      main: {
        entry: 'background.js',
      },
      preload: {
        input: 'src/preload/preload.js',
      },
      renderer: {},
    }),
  ],
  build: {
    outDir: 'dist',
  },
  server: {
    port: 3000,
  },
});
