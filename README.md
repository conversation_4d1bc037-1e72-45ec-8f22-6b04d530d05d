# ElecVueProGen

A Vue.js 3 + Electron application for generating and managing AI prompts with a user-friendly interface.

## Features

- **Prompt Generation**: Create prompts by selecting from predefined categories
- **Editable Dropdowns**: Customize dropdown options with your own content
- **Checkbox Options**: Toggle additional prompt elements
- **Save & Load**: Save prompts to JSON files for later use
- **Clipboard Support**: Copy prompts to clipboard with one click
- **Cross-Platform**: Works on Windows, macOS, and Linux

## Prerequisites

- Node.js (v16 or higher)
- npm (v7 or higher) or yarn

## Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/LADLABS/ElecVueProGen.git
   cd ElecVueProGen
   ```

2. Install dependencies:
   ```bash
   npm install
   # or
   yarn
   ```

## Usage

### Development Mode

To run the application in development mode:

```bash
npm run electron:serve
# or
yarn electron:serve
```

This will start the Vue development server and the Electron application, allowing for live reloads as you make changes.

### Production Build

To build the application for production:

```bash
# Build for current platform
npm run electron:build
# or
yarn electron:build

# Build for specific platform (e.g., Windows, macOS, Linux)
npm run electron:build -- --win --mac --linux
# or
yarn electron:build --win --mac --linux
```

The built application executables will be located in the `dist_electron` directory.

### Running the Built Application

After building for production, you can find the executable in the `dist_electron` directory.

- **Windows**: `dist_electron/ElecVueProGen Setup <version>.exe`
- **macOS**: `dist_electron/ElecVueProGen-<version>.dmg` or `dist_electron/ElecVueProGen-<version>.zip`
- **Linux**: `dist_electron/ElecVueProGen-<version>.AppImage` or `dist_electron/ElecVueProGen-<version>.deb`

Simply run the appropriate executable for your operating system.

## Project Structure

```
elec-vue-pro-gen/
├── src/
│   ├── assets/         # Static assets
│   │   └── data/       # Default data files
│   ├── components/     # Vue components
│   ├── store/          # Pinia store
│   ├── preload/        # Electron preload scripts
│   ├── App.vue         # Root component
│   └── main.js         # Application entry point
├── background.js       # Electron main process
├── index.html          # Main HTML file
├── package.json        # Project configuration
├── tailwind.config.js  # Tailwind CSS config
└── vite.config.js      # Vite configuration
```

## Data Files

The application uses JSON files to store prompt categories and options. These are stored in a user-configurable data folder (defaults to `~/ElecVueProGenData`).

Example structure for a category file (`role.json`):

```json
{
  "items": [
    "You are an expert AI assistant",
    "You are a helpful assistant",
    "You are a senior software engineer"
  ]
}
```

## License

MIT
