const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // File operations
  readJson: (filename) => ipcRenderer.invoke('read-json', filename),
  writeJson: (filename, data) => ipcRenderer.invoke('write-json', filename, data),
  savePrompt: (promptData) => ipcRenderer.invoke('save-prompt', promptData),
  
  // Clipboard operations
  copyToClipboard: (text) => ipcRenderer.invoke('copy-to-clipboard', text),
  
  // Window events
  onDataFolderSelected: (callback) => 
    ipcRenderer.on('data-folder-selected', (_, folder) => callback(folder)),
  
  // Menu events
  selectFolder: () => ipcRenderer.invoke('select-folder'),

  // Data loading
  loadData: () => ipcRenderer.invoke('load-data')
});
