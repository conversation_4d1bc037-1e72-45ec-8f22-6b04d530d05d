<template>
  <div class="flex flex-col h-full bg-white border-l border-gray-200">
    <!-- Header -->
    <div class="p-4 border-b border-gray-200">
      <h2 class="text-lg font-semibold text-gray-800">Main Prompt</h2>
    </div>

    <!-- Prompt Editor -->
    <div class="flex-1 p-4 min-h-0">
      <textarea
        v-model="store.mainPrompt"
        class="w-full h-full p-2 border border-gray-300 rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-indigo-500"
        placeholder="Your generated prompt will appear here..."
      ></textarea>
    </div>

    <!-- Button Row -->
    <div class="p-4 border-t border-gray-200">
      <div class="flex space-x-3">
        <button
          @click="copyToClipboard"
          class="px-4 py-2 bg-indigo-600 text-white font-semibold rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
        >
          Copy
        </button>
        <button
          @click="clearPrompt"
          class="px-4 py-2 bg-gray-600 text-white font-semibold rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors"
        >
          Clear
        </button>
        <button
          @click="savePrompt"
          class="px-4 py-2 bg-green-600 text-white font-semibold rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors"
        >
          Save
        </button>
      </div>
    </div>

    <!-- Saved Prompts List -->
    <div class="flex-1 p-4 border-t border-gray-200 min-h-0">
      <div class="h-full flex flex-col">
        <h3 class="text-md font-semibold text-gray-800 mb-3">Saved Prompts</h3>
        <div class="flex-1 overflow-y-auto space-y-2 min-h-0 custom-scrollbar">
          <div
            v-if="store.savedPrompts.length === 0"
            class="text-gray-500 text-sm italic text-center py-8"
          >
            No saved prompts yet. Save your first prompt using the Save button above.
          </div>
          <div
            v-for="prompt in store.savedPrompts"
            :key="prompt.id"
            class="p-3 border border-gray-200 rounded-md hover:bg-gray-50 hover:border-gray-300 cursor-pointer transition-all duration-200 group shadow-sm hover:shadow-md"
            @click="loadSavedPrompt(prompt.id)"
          >
            <div class="flex justify-between items-start mb-2">
              <span class="text-xs text-gray-500 font-medium">
                {{ formatDate(prompt.createdAt) }}
              </span>
              <button
                @click.stop="removeSavedPrompt(prompt.id)"
                class="text-gray-400 hover:text-red-500 opacity-0 group-hover:opacity-100 transition-all duration-200 p-1 rounded hover:bg-red-50"
                title="Remove prompt"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
            <div class="text-sm text-gray-700 whitespace-pre-wrap break-words leading-relaxed">
              {{ prompt.content }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useAppStore } from '../store';

const store = useAppStore();

const copyToClipboard = () => {
  if (!store.mainPrompt.trim()) {
    alert('No prompt to copy!');
    return;
  }

  navigator.clipboard.writeText(store.mainPrompt).then(() => {
    alert('Prompt copied to clipboard!');
  }).catch(err => {
    console.error('Failed to copy text: ', err);
    alert('Failed to copy prompt.');
  });
};

const clearPrompt = () => {
  if (store.mainPrompt.trim() && !confirm('Are you sure you want to clear the prompt?')) {
    return;
  }
  store.clearMainPrompt();
};

const savePrompt = async () => {
  if (!store.mainPrompt.trim()) {
    alert('No prompt to save!');
    return;
  }

  const success = await store.saveMainPromptToList();
  if (success) {
    alert('Prompt saved successfully!');
  } else {
    alert('Failed to save prompt.');
  }
};

const loadSavedPrompt = (promptId) => {
  if (store.mainPrompt.trim() && !confirm('Loading a saved prompt will replace the current content. Continue?')) {
    return;
  }

  const success = store.loadPromptFromSaved(promptId);
  if (!success) {
    alert('Failed to load prompt.');
  }
};

const removeSavedPrompt = (promptId) => {
  if (!confirm('Are you sure you want to delete this saved prompt?')) {
    return;
  }

  const success = store.removeSavedPrompt(promptId);
  if (!success) {
    alert('Failed to remove prompt.');
  }
};

const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};
</script>

<style scoped>
/* Custom scrollbar for saved prompts list */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Firefox scrollbar */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}
</style>
