<template>
  <div class="space-y-6 p-4">
  
    <!-- Categories Section -->
    <div v-for="category in store.categories" :key="category.id" class="space-y-2">
      <label :for="category.id" class="block text-sm font-medium text-gray-700">
        {{ category.name }}
      </label>
      <div class="flex space-x-2">
        <div class="flex flex-col flex-1 min-w-0">
          <select
            :id="category.id"
            v-model="category.selected"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            :class="{ 'border-green-500': store.isItemUsed(`category-${category.id}`) }"
          >
            <option value="">Select {{ category.name.toLowerCase() }}</option>
            <option v-for="(item, index) in category.items" :key="index" :value="item">
              {{ item }}
            </option>
          </select>
          <div class="flex items-center mt-1">
            <input
              v-model="newItems[category.id]"
              @keyup.enter="addDropdownItem(category)"
              type="text"
              class="flex-1 px-2 py-1 border border-gray-300 rounded mr-2 text-sm"
              :placeholder="`Add new ${category.name.toLowerCase()}`"
            />
            <button
              @click="addDropdownItem(category)"
              class="px-2 py-1 bg-gray-200 rounded text-xs hover:bg-gray-300"
              title="Add item"
            >+
            </button>
            <button
              v-if="category.items.length > 0"
              @click="removeDropdownItem(category)"
              class="ml-1 px-2 py-1 bg-red-200 rounded text-xs hover:bg-red-300"
              title="Remove selected"
            >-
            </button>
          </div>
        </div>
        <button
          @click="handleAdd(category)"
          class="px-3 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          :class="{ 'bg-green-600 hover:bg-green-700': store.isItemUsed(`category-${category.id}`) }"
        >
          Add
        </button>
      </div>
    </div>

    <!-- Divider -->
    <div class="border-t border-gray-200"></div>

    <!-- Checkboxes Section -->
    <div v-for="checkbox in store.checkboxes" :key="checkbox.id" class="flex items-start">
      <div class="flex items-center h-5">
        <CheckboxRoot v-model:checked="checkbox.checked" :id="checkbox.id" class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded flex items-center justify-center">
          <CheckboxIndicator>
            <svg class="w-3 h-3 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M16.707 6.293a1 1 0 00-1.414 0L9 12.586l-2.293-2.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l7-7a1 1 0 000-1.414z" clip-rule="evenodd" />
            </svg>
          </CheckboxIndicator>
        </CheckboxRoot>
      </div>
      <div class="ml-3 text-sm">
        <label :for="checkbox.id" class="font-medium text-gray-700">
          {{ checkbox.label }}
        </label>
      </div>
      <button
        @click="handleAddCheckbox(checkbox)"
        class="ml-auto px-3 py-1 text-sm bg-indigo-600 text-white rounded hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        :class="{ 'bg-green-600 hover:bg-green-700': checkbox.used }"
      >
        Add
      </button>
    </div>
  </div>
</template>

<script setup>
import { reactive, onMounted, watch } from 'vue';
import { useAppStore } from '../store';
import { CheckboxRoot, CheckboxIndicator } from 'radix-vue';

const store = useAppStore();

const newItems = reactive({});
const selectedPromptElements = reactive({
  role: '',
  objective: '',
  requirement: '',
  constraint: '',
  approach: ''
});

const capitalize = (str) => {
  if (typeof str !== 'string' || str.length === 0) return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
};

const handleAddPromptElement = (key) => {
  const value = selectedPromptElements[key];
  if (value) {
    store.appendToMainPrompt(`${capitalize(key)}: ${value}`);
  }
};

const addDropdownItem = (category) => {
  const val = (newItems[category.id] || '').trim();
  if (val && !category.items.includes(val)) {
    category.items.push(val);
    newItems[category.id] = '';
  }
};

const removeDropdownItem = (category) => {
  if (category.selected) {
    const idx = category.items.indexOf(category.selected);
    if (idx !== -1) {
      category.items.splice(idx, 1);
      category.selected = '';
    }
  }
};

const handleAdd = (category) => {
  if (category.selected) {
    const key = `category-${category.id}`;
    // Add to the main prompt editor with formatted category name
    store.appendFormattedToMainPrompt(category.name, category.selected);
    // Also track usage for UI feedback
    store.usedItems.add(key);
  }
};

const handleAddCheckbox = (checkbox) => {
  if (checkbox.checked) {
    // Add to the main prompt editor with formatted category name
    store.appendFormattedToMainPrompt('Instructions', checkbox.label);
    // Track usage for UI feedback
    store.usedItems.add(`checkbox-${checkbox.id}`);
    checkbox.used = true;
  }
};

onMounted(() => {
  store.fetchData();
  
  watch(() => store.categories, (newCategories) => {
    newCategories.forEach(cat => {
      if (newItems[cat.id] === undefined) {
        newItems[cat.id] = '';
      }
    });
  }, { immediate: true, deep: true });
});
</script>

<style scoped>
/* No additional styles needed as Tailwind CSS is used. */
</style>
