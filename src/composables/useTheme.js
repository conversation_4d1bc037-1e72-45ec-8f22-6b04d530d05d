import { ref, readonly } from 'vue';

const isDark = ref(false);
const THEME_KEY = 'prompt-crafter-theme';

export function useTheme() {
  const toggleTheme = () => {
    isDark.value = !isDark.value;
    applyTheme();
    saveTheme();
  };

  const setTheme = (dark) => {
    isDark.value = dark;
    applyTheme();
    saveTheme();
  };

  const applyTheme = () => {
    const html = document.documentElement;
    if (isDark.value) {
      html.classList.add('dark');
    } else {
      html.classList.remove('dark');
    }
  };

  const saveTheme = () => {
    try {
      localStorage.setItem(THEME_KEY, JSON.stringify(isDark.value));
    } catch (error) {
      console.warn('Failed to save theme preference:', error);
    }
  };

  const loadTheme = () => {
    try {
      const saved = localStorage.getItem(THEME_KEY);
      if (saved !== null) {
        isDark.value = JSON.parse(saved);
      } else {
        // Default to system preference
        isDark.value = window.matchMedia('(prefers-color-scheme: dark)').matches;
      }
    } catch (error) {
      console.warn('Failed to load theme preference:', error);
      isDark.value = false;
    }
    applyTheme();
  };

  const initTheme = () => {
    loadTheme();
    
    // Listen for system theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleSystemThemeChange = (e) => {
      // Only update if user hasn't manually set a preference
      const saved = localStorage.getItem(THEME_KEY);
      if (saved === null) {
        isDark.value = e.matches;
        applyTheme();
      }
    };
    
    mediaQuery.addEventListener('change', handleSystemThemeChange);
    
    return () => {
      mediaQuery.removeEventListener('change', handleSystemThemeChange);
    };
  };

  return {
    isDark: readonly(isDark),
    toggleTheme,
    setTheme,
    initTheme
  };
}

// Global theme state
const { isDark: globalIsDark, toggleTheme: globalToggleTheme, setTheme: globalSetTheme, initTheme: globalInitTheme } = useTheme();

export { globalIsDark as isDark, globalToggleTheme as toggleTheme, globalSetTheme as setTheme, globalInitTheme as initTheme };
