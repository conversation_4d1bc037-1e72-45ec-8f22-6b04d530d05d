<template>
  <div id="app" class="flex h-screen bg-gray-100">
    <div class="w-1/3 max-w-sm overflow-y-auto bg-white shadow-md">
      <LeftPanel />
    </div>
    <div class="flex-1">
      <MainPanel />
    </div>
  </div>
</template>

<script setup>
import LeftPanel from './components/LeftPanel.vue';
import MainPanel from './components/MainPanel.vue';
</script>

<style>
/* Global styles if needed */
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}
</style>
