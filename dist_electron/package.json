{"name": "prompt-crafter", "version": "1.0.0", "description": "A Vue.js + Electron application for crafting AI prompts", "main": "background.js", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "electron:serve": "vue-cli-service electron:serve", "electron:build": "vue-cli-service electron:build", "postinstall": "electron-builder install-app-deps", "postuninstall": "electron-builder install-app-deps"}, "dependencies": {"@headlessui/vue": "^1.7.14", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "lucide-vue-next": "^0.344.0", "pinia": "^2.1.7", "radix-vue": "^1.9.17", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "vue": "^3.3.4", "vue-router": "^4.2.5"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@vitejs/plugin-vue": "^4.3.4", "@vue/cli-service": "^5.0.8", "autoprefixer": "^10.4.15", "electron": "^25.3.2", "electron-builder": "^24.4.0", "postcss": "^8.4.29", "tailwindcss": "^3.3.3", "vite": "^4.4.9", "vite-plugin-electron-renderer": "^0.14.3", "vite-plugin-vue-devtools": "^7.0.8", "vue-cli-plugin-electron-builder": "^2.1.1"}}