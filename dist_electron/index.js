/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, { enumerable: true, get: getter });
/******/ 		}
/******/ 	};
/******/
/******/ 	// define __esModule on exports
/******/ 	__webpack_require__.r = function(exports) {
/******/ 		if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 			Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 		}
/******/ 		Object.defineProperty(exports, '__esModule', { value: true });
/******/ 	};
/******/
/******/ 	// create a fake namespace object
/******/ 	// mode & 1: value is a module id, require it
/******/ 	// mode & 2: merge all properties of value into the ns
/******/ 	// mode & 4: return value when already ns object
/******/ 	// mode & 8|1: behave like require
/******/ 	__webpack_require__.t = function(value, mode) {
/******/ 		if(mode & 1) value = __webpack_require__(value);
/******/ 		if(mode & 8) return value;
/******/ 		if((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;
/******/ 		var ns = Object.create(null);
/******/ 		__webpack_require__.r(ns);
/******/ 		Object.defineProperty(ns, 'default', { enumerable: true, value: value });
/******/ 		if(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));
/******/ 		return ns;
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "";
/******/
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = 0);
/******/ })
/************************************************************************/
/******/ ({

/***/ "./background.js":
/*!***********************!*\
  !*** ./background.js ***!
  \***********************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("const { app, BrowserWindow, ipcMain, dialog, Menu } = __webpack_require__(/*! electron */ \"electron\");\nconst path = __webpack_require__(/*! path */ \"path\");\nconst fs = __webpack_require__(/*! fs */ \"fs\").promises;\nconst fsSync = __webpack_require__(/*! fs */ \"fs\");\nconst os = __webpack_require__(/*! os */ \"os\");\n\nlet mainWindow;\nlet dataFolder = path.join(os.homedir(), 'PromptCrafterData');\n\n// Ensure data directory exists\nconst ensureDataDir = async () => {\n  try {\n    await fs.mkdir(dataFolder, { recursive: true });\n    \n    // Create default JSON files if they don't exist\n    const defaultFiles = {\n      'role.json': { items: [\"You are an expert AI assistant\", \"You are a helpful assistant\", \"You are a senior software engineer\"] },\n      'objective.json': { items: [\"Create a detailed plan for\", \"Develop a solution for\", \"Explain how to\"] },\n      'feature.json': { items: [\"with responsive design\", \"with user authentication\", \"with real-time updates\"] },\n      'constraint.json': { items: [\"while following best practices\", \"within a tight deadline\", \"with minimal dependencies\"] },\n      'approach.json': { items: [\"using a step-by-step approach\", \"with clear examples\", \"with code samples\"] }\n    };\n\n    for (const [filename, content] of Object.entries(defaultFiles)) {\n      const filePath = path.join(dataFolder, filename);\n      if (!fsSync.existsSync(filePath)) {\n        await fs.writeFile(filePath, JSON.stringify(content, null, 2));\n      }\n    }\n  } catch (error) {\n    console.error('Error setting up data directory:', error);\n  }\n};\n\nconst createWindow = async () => {\n  await ensureDataDir();\n  \n  mainWindow = new BrowserWindow({\n    width: 1200,\n    height: 800,\n    webPreferences: {\n      nodeIntegration: false,\n      contextIsolation: true,\n      preload: path.join(__dirname, 'preload.js') // Corrected path for Vue CLI\n    },\n    title: 'Prompt Crafter',\n    icon: path.join(__dirname, 'public/icon.png')\n  });\n\n  if (true) {\n    // Load the url of the dev server if in development mode\n    await mainWindow.loadURL(\"http://localhost:8080/\");\n    if (!process.env.IS_TEST) mainWindow.webContents.openDevTools();\n  } else {}\n\n  createMenu();\n};\n\nconst createMenu = () => {\n  const menuTemplate = [\n    {\n      label: 'File',\n      submenu: [\n        {\n          label: 'Select Data Folder',\n          click: async () => {\n            const result = await dialog.showOpenDialog({\n              properties: ['openDirectory']\n            });\n            \n            if (!result.canceled && result.filePaths.length > 0) {\n              dataFolder = result.filePaths[0];\n              await ensureDataDir();\n              mainWindow.webContents.send('data-folder-selected', dataFolder);\n            }\n          }\n        },\n        { type: 'separator' },\n        { role: 'quit' }\n      ]\n    },\n    {\n      label: 'Edit',\n      submenu: [\n        { role: 'undo' },\n        { role: 'redo' },\n        { type: 'separator' },\n        { role: 'cut' },\n        { role: 'copy' },\n        { role: 'paste' },\n        { role: 'delete' },\n        { type: 'separator' },\n        { role: 'selectAll' }\n      ]\n    },\n    {\n      label: 'View',\n      submenu: [\n        { role: 'reload' },\n        { role: 'forceReload' },\n        { role: 'toggleDevTools' },\n        { type: 'separator' },\n        { role: 'resetZoom' },\n        { role: 'zoomIn' },\n        { role: 'zoomOut' },\n        { type: 'separator' },\n        { role: 'togglefullscreen' }\n      ]\n    },\n    {\n      label: 'About',\n      submenu: [\n        {\n          label: 'About Prompt Crafter',\n          click: () => {\n            dialog.showMessageBox(mainWindow, {\n              type: 'info',\n              title: 'About Prompt Crafter',\n              message: 'Prompt Crafter',\n              detail: 'A professional tool for crafting AI prompts with Vue.js and Electron.'\n            });\n          }\n        }\n      ]\n    }\n  ];\n\n  const menu = Menu.buildFromTemplate(menuTemplate);\n  Menu.setApplicationMenu(menu);\n};\n\n// Handle file operations\nipcMain.handle('read-json', async (_, filename) => {\n  try {\n    const filePath = path.join(dataFolder, filename);\n    const data = await fs.readFile(filePath, 'utf-8');\n    return JSON.parse(data);\n  } catch (error) {\n    console.error(`Error reading ${filename}:`, error);\n    return { items: [] };\n  }\n});\n\nipcMain.handle('write-json', async (_, filename, data) => {\n  try {\n    const filePath = path.join(dataFolder, filename);\n    await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf-8');\n    return true;\n  } catch (error) {\n    console.error(`Error writing ${filename}:`, error);\n    return false;\n  }\n});\n\nipcMain.handle('save-prompt', async (_, promptData) => {\n  try {\n    const promptsPath = path.join(dataFolder, 'prompts.json');\n    let prompts = [];\n    \n    if (fsSync.existsSync(promptsPath)) {\n      const data = await fs.readFile(promptsPath, 'utf-8');\n      prompts = JSON.parse(data);\n    }\n    \n    prompts.push({\n      id: Date.now(),\n      content: promptData,\n      createdAt: new Date().toISOString()\n    });\n    \n    await fs.writeFile(promptsPath, JSON.stringify(prompts, null, 2));\n    return true;\n  } catch (error) {\n    console.error('Error saving prompt:', error);\n    return false;\n  }\n});\n\n// Handle data loading\nipcMain.handle('load-data', async () => {\n  const data = {};\n  try {\n    // Load static data from the public folder\n    const staticDataPath =  true\n      ? path.join(__dirname, '../public')\n      : undefined;\n    const staticFiles = ['categories.json', 'checkboxes.json', 'role.json', 'objective.json', 'feature.json', 'constraint.json', 'approach.json'];\n    for (const file of staticFiles) {\n      const filePath = path.join(staticDataPath, file);\n      const fileContent = await fs.readFile(filePath, 'utf-8');\n      data[file.replace('.json', '')] = JSON.parse(fileContent);\n    }\n\n    // Load user-specific prompts\n    const promptsPath = path.join(dataFolder, 'prompts.json');\n    if (fsSync.existsSync(promptsPath)) {\n      const promptsContent = await fs.readFile(promptsPath, 'utf-8');\n      data['prompts'] = JSON.parse(promptsContent);\n    } else {\n      data['prompts'] = [];\n    }\n\n    return data;\n  } catch (error) {\n    console.error('Error loading initial data:', error);\n    return null;\n  }\n});\n\n// App lifecycle events\napp.whenReady().then(createWindow);\n\napp.on('window-all-closed', () => {\n  if (process.platform !== 'darwin') {\n    app.quit();\n  }\n});\n\napp.on('activate', () => {\n  if (BrowserWindow.getAllWindows().length === 0) {\n    createWindow();\n  }\n});\n\n\n//# sourceURL=webpack:///./background.js?");

/***/ }),

/***/ 0:
/*!*****************************!*\
  !*** multi ./background.js ***!
  \*****************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("module.exports = __webpack_require__(/*! /home/<USER>/Desktop/dev/ElecVueProGen/background.js */\"./background.js\");\n\n\n//# sourceURL=webpack:///multi_./background.js?");

/***/ }),

/***/ "electron":
/*!***************************!*\
  !*** external "electron" ***!
  \***************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("module.exports = require(\"electron\");\n\n//# sourceURL=webpack:///external_%22electron%22?");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("module.exports = require(\"fs\");\n\n//# sourceURL=webpack:///external_%22fs%22?");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("module.exports = require(\"os\");\n\n//# sourceURL=webpack:///external_%22os%22?");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("module.exports = require(\"path\");\n\n//# sourceURL=webpack:///external_%22path%22?");

/***/ })

/******/ });